# Importer les bibliothèques nécessaires
import pandas as pd
import os

# Définir le chemin du dossier source
dossier_source = r"C:\Users\<USER>\Desktop\RIKOLTO GLOBAL MEL\Rice SRP Farmer Survey Data\Rice Burkina\2022-2023 Datasets\SRP Standard"

# Lister les fichiers du dossier
fichiers = [f for f in os.listdir(dossier_source) if f.endswith('.csv') or f.endswith('.xlsx')]
print("Fichiers trouvés :", fichiers)

# Charger un fichier exemple (remplace par le nom réel si besoin)
fichier_exemple = fichiers[0]
chemin_fichier = os.path.join(dossier_source, fichier_exemple)

# Lire le fichier (adapter selon le format)
if fichier_exemple.endswith('.csv'):
    df = pd.read_csv(chemin_fichier)
else:
    df = pd.read_excel(chemin_fichier)

# Afficher les premières lignes pour vérifier le chargement
df.head()

# Afficher les informations générales sur le DataFrame
print("Dimensions :", df.shape)
print("Colonnes :", df.columns.tolist())

# Vérifier les valeurs manquantes
print("\nValeurs manquantes par colonne :")
print(df.isnull().sum())

# Vérifier les doublons
nb_doublons = df.duplicated().sum()
print(f"\nNombre de doublons : {nb_doublons}")

# Supprimer les doublons si besoin
df = df.drop_duplicates()

# Afficher un aperçu après nettoyage
df.head()

import pandas as pd
import os

# Définir le chemin du fichier
chemin_fichier = r"C:\Users\<USER>\Desktop\RIKOLTO GLOBAL MEL\Rice SRP Farmer Survey Data\Rice Burkina\2022-2023 Datasets\SRP Standard\2.1_Burkina_Bagre_SRP_Analysis 2023.xlsx"

# Lister les noms des feuilles
feuilles = pd.ExcelFile(chemin_fichier).sheet_names
print("Feuilles trouvées :", feuilles)

# Parcourir et nettoyer chaque feuille
dfs = {}
for feuille in feuilles:
    # Lire la feuille en sautant les 4 premières lignes (header à la 5e ligne)
    df = pd.read_excel(chemin_fichier, sheet_name=feuille, header=4)
    # Supprimer les colonnes et lignes vides
    df = df.dropna(axis=1, how='all')
    df = df.dropna(axis=0, how='all')
    # Supprimer les doublons
    df = df.drop_duplicates()
    # Stocker le DataFrame nettoyé
    dfs[feuille] = df
    print(f"{feuille} : {df.shape[0]} lignes, {df.shape[1]} colonnes")

# Exemple : afficher les 5 premières lignes de la première feuille nettoyée
dfs[feuilles[0]].head()

# Afficher les premières lignes de la feuille "subgroup scores"
df_subgroup = dfs['subgroup scores']
print("Colonnes :", df_subgroup.columns.tolist())
df_subgroup.head()

# Afficher les 10 premiers indicateurs (lignes)
print(df_subgroup[['r', '(Multiple Items)', 'Column Labels']].head(10))

print(df_subgroup.columns.tolist())

# Afficher les 10 premières lignes avec les colonnes principales et les premières colonnes de scores
colonnes_a_afficher = ['Unnamed: 0', '(Multiple Items)', 'Column Labels'] + df_subgroup.columns[4:14].tolist()
print(df_subgroup[colonnes_a_afficher].head(10))

# Sélectionner la ligne correspondant à "Crop calendar"
ligne_crop_calendar = df_subgroup[df_subgroup['(Multiple Items)'] == 'Crop calendar']

# Afficher les scores pour tous les sous-groupes
print(ligne_crop_calendar)

# Renommer les colonnes principales pour plus de clarté
df_subgroup = df_subgroup.rename(columns={
    'Unnamed: 0': 'Code Indicateur',
    '(Multiple Items)': 'Indicateur',
    'Column Labels': 'Type Valeur'
})

# Afficher les 10 premières lignes avec les nouveaux noms de colonnes
colonnes_a_afficher = ['Code Indicateur', 'Indicateur', 'Type Valeur'] + df_subgroup.columns[3:13].tolist()
print(df_subgroup[colonnes_a_afficher].head(10))

import seaborn as sns
import matplotlib.pyplot as plt

# Sélectionner les lignes d'indicateurs et les colonnes de scores
scores = df_subgroup.iloc[1:, 4:14]  # adapter selon tes colonnes de scores
scores.index = df_subgroup['Indicateur'].iloc[1:11]  # adapter selon le nombre d'indicateurs

plt.figure(figsize=(10,6))
sns.heatmap(scores.astype(float), annot=True, cmap="YlGnBu")
plt.title("Scores des indicateurs par sous-groupe")
plt.xlabel("Sous-groupe")
plt.ylabel("Indicateur")
plt.show()

# Installer seaborn dans le notebook
!pip install seaborn

pip install seaborn

import seaborn as sns
import matplotlib.pyplot as plt

# Sélectionner les lignes d'indicateurs et les colonnes de scores
scores = df_subgroup.iloc[1:, 4:14]  # adapter selon tes colonnes de scores
scores.index = df_subgroup['Indicateur'].iloc[1:11]  # adapter selon le nombre d'indicateurs

plt.figure(figsize=(10,6))
sns.heatmap(scores.astype(float), annot=True, cmap="YlGnBu")
plt.title("Scores des indicateurs par sous-groupe")
plt.xlabel("Sous-groupe")
plt.ylabel("Indicateur")
plt.show()

import seaborn as sns
import matplotlib.pyplot as plt

# Sélectionner les 10 premiers indicateurs et les 10 premiers sous-groupes
scores = df_subgroup.iloc[1:11, 4:14]  # 10 lignes, 10 colonnes de scores
scores.index = df_subgroup['Indicateur'].iloc[1:11]

plt.figure(figsize=(10,6))
sns.heatmap(scores.astype(float), annot=True, cmap="YlGnBu")
plt.title("Scores des 10 premiers indicateurs par sous-groupe")
plt.xlabel("Sous-groupe")
plt.ylabel("Indicateur")
plt.show()

import matplotlib.pyplot as plt
import matplotlib.animation as animation
import seaborn as sns
import numpy as np

fig, ax = plt.subplots(figsize=(10,6))

def update(i):
    ax.clear()
    # Affiche 5 sous-groupes à la fois
    data = scores.iloc[:, i:i+5]
    sns.heatmap(data.astype(float), annot=True, cmap="YlGnBu", ax=ax)
    ax.set_title(f"Sous-groupes {i+1} à {i+5}")

ani = animation.FuncAnimation(fig, update, frames=np.arange(0, scores.shape[1]-4), interval=1500)
plt.show()

ani.save("heatmap_animation.gif", writer="pillow")